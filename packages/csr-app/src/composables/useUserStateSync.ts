import { watch, onMounted, onUnmounted } from 'vue'
import { useUserStore } from '@/store/user'
import { sendMessageToParent } from '@/main'

/**
 * 用户状态同步 composable
 * 监听用户信息变化并自动同步到主应用
 */
export function useUserStateSync() {
  const userStore = useUserStore()

  // 防抖函数，避免频繁同步
  let syncTimer: NodeJS.Timeout | null = null

  const debouncedSync = () => {
    if (syncTimer) {
      clearTimeout(syncTimer)
    }

    syncTimer = setTimeout(() => {
      syncUserStateToParent()
    }, 300) // 300ms 防抖
  }

  // 同步用户状态到主应用
  const syncUserStateToParent = () => {
    if (!userStore.userInfo || !userStore.token) {
      console.log('💎 CSR应用: 同步跳过，缺少必要信息', {
        hasUserInfo: !!userStore.userInfo,
        hasToken: !!userStore.token,
      })
      return
    }

    const payload = {
      userInfo: userStore.userInfo,
      token: userStore.token,
      userId: userStore.userId,
      timestamp: Date.now(),
    }

    console.log('💎 CSR应用: 准备同步用户状态到主应用', payload)
    sendMessageToParent('USER_STATE_UPDATE', payload)
    console.log(
      '💎 CSR应用: 用户状态已同步到主应用，钻石数量:',
      userStore.userInfo.coins,
    )
  }

  // 监听用户信息变化
  const stopWatchingUserInfo = watch(
    () => userStore.userInfo,
    (newUserInfo, oldUserInfo) => {
      if (!newUserInfo) return

      // 检查关键字段是否发生变化
      const coinsChanged = newUserInfo.coins !== oldUserInfo?.coins
      const nameChanged = newUserInfo.name !== oldUserInfo?.name
      const avatarChanged = newUserInfo.avatar_url !== oldUserInfo?.avatar_url

      if (coinsChanged || nameChanged || avatarChanged) {
        console.log('💎 CSR应用: 检测到用户信息变化', {
          coins: { old: oldUserInfo?.coins, new: newUserInfo.coins },
          name: { old: oldUserInfo?.name, new: newUserInfo.name },
          avatar: { old: oldUserInfo?.avatar_url, new: newUserInfo.avatar_url },
        })

        debouncedSync()
      }
    },
    { deep: true, immediate: false },
  )

  // 监听认证状态变化
  const stopWatchingAuth = watch(
    () => [userStore.token, userStore.userId],
    ([newToken, newUserId], [oldToken, oldUserId]) => {
      if (newToken !== oldToken || newUserId !== oldUserId) {
        console.log('🔐 CSR应用: 检测到认证状态变化')
        debouncedSync()
      }
    },
  )

  // 初始同步（延迟执行，确保应用完全初始化）
  onMounted(() => {
    console.log('🔄 CSR应用: useUserStateSync 已启动')
    setTimeout(() => {
      if (userStore.userInfo && userStore.token) {
        console.log('🔄 CSR应用: 执行初始用户状态同步')
        syncUserStateToParent()
      } else {
        console.log('🔄 CSR应用: 初始同步跳过，用户信息或token不存在', {
          hasUserInfo: !!userStore.userInfo,
          hasToken: !!userStore.token,
        })
      }
    }, 1000)
  })

  // 清理函数
  const cleanup = () => {
    if (syncTimer) {
      clearTimeout(syncTimer)
      syncTimer = null
    }
    stopWatchingUserInfo()
    stopWatchingAuth()
  }

  // 组件卸载时清理
  onUnmounted(cleanup)

  return {
    syncUserStateToParent,
    cleanup,
  }
}
