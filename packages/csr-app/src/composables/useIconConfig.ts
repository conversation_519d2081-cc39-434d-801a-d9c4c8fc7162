/**
 * 图标配置管理
 * 统一管理所有图标地址，避免硬编码
 */

import { computed } from 'vue'

export const useIconConfig = () => {
  // 获取当前品牌配置
  const appName = import.meta.env.VITE_APP_NAME || 'ReelPlay'
  const isReelPlay = appName.toLowerCase().includes('reel')

  // 根据品牌确定 CDN 基础地址
  const cdnBase = computed(() => {
    return isReelPlay
      ? 'https://static.reelplay.ai'
      : 'https://static.playshot.ai'
  })

  /**
   * 构建图标 URL
   */
  const getIconUrl = (iconName: string): string => {
    return `${cdnBase.value}/static/images/icon/${iconName}`
  }

  /**
   * 预定义的常用图标配置
   */
  const icons = {
    // 应用图标
    appIcon: computed(() => getIconUrl('playshot-icon.png')),
    favicon: computed(() => getIconUrl('playshot-icon.png')),

    // 功能图标
    diamond: computed(() => getIconUrl('diamond.png')),
    coin: computed(() => getIconUrl('diamond.png')), // 钻石和金币使用同一个图标

    // 默认头像
    defaultAvatar: computed(() => getIconUrl('default-avatar.png')),

    // 其他常用图标
    logo: computed(
      () =>
        import.meta.env.VITE_LOGO_URL ||
        'https://cdn.magiclight.ai/assets/playshot/logo-v2.png',
    ),
  }

  return {
    icons,
    getIconUrl,
    cdnBase,
  }
}
